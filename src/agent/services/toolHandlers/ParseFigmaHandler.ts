import { ToolUse } from '../../types/message';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ToolHandlerContext, ToolHand<PERSON> } from '../ToolHelpers';
import { TextBlockParamVersion1, SayTool } from '../../types/type.d';
import { ASSISTANT_NAMESPACE } from '@/util/const';
import { convertFigmaJson } from '../../tools/parse-figma';
import { LangfuseGenerationClient } from 'langfuse';
import { FigmaTokenHelper } from './FigmaTokenHelper';
import { TokenCalculator } from '../../context/TokenCalculator';

/**
 * 解析 Figma 工具处理器
 */
export class ParseFigmaHandler implements ToolHandler {
  constructor(private context: ToolHandlerContext) {}

  async handle(block: ToolUse, userMessageContent: TextBlockParamVersion1[]): Promise<void> {
    let generationCall: LangfuseGenerationClient | null | undefined = null;

    const url: string | undefined = block.params.url;
    const sharedMessageProps: SayTool = {
      tool: 'parseFigma',
      url: ToolHelpers.removeClosingTag('url', url)
    };

    try {
      if (block.partial) {
        const partialMessage = JSON.stringify({
          ...sharedMessageProps,
          content: ''
        } satisfies SayTool);
        await this.context.messageService.say('tool', partialMessage, block.partial);
        return;
      } else {
        if (!url) {
          this.context.stateManager.updateState({
            consecutiveMistakeCount: this.context.stateManager.getState().consecutiveMistakeCount + 1
          });
          const errorMessage = await ToolHelpers.sayAndCreateMissingParamError('parse_figma', 'url', this.context);
          ToolHelpers.pushToolResult(block, userMessageContent, errorMessage, this.context.stateManager);
          return;
        }

        this.context.stateManager.updateState({ consecutiveMistakeCount: 0 });

        this.context.loggerManager.reportUserAction({
          key: 'agent_tools_request',
          type: 'parse_figma'
        });

        const toolLog = ToolHelpers.generateToolLog('parse_figma', this.context.loggerManager);
        toolLog.start(url);

        generationCall = this.context.loggerManager.getTrace()?.generation({
          name: 'tool_call',
          input: {
            url
          },
          metadata: {
            name: block.name
          }
        });

        const startToolTime = Date.now();

        // 解析 Figma Node JSON
        const [result, rootImageUrl] = await convertFigmaJson(url, {
          getTokenFn: () => FigmaTokenHelper.getEnvironmentFigmaToken(this.context.messenger),
          imageConfig: {
            fetchImage: true
          }
        });

        toolLog.end(result);
        generationCall?.end({
          output: { result }
        });

        const successMessage = JSON.stringify({
          ...sharedMessageProps,
          content: result
        } satisfies SayTool);
        ToolHelpers.pushToolResult(block, userMessageContent, [
        {
          type: 'text',
          text: successMessage
        }], this.context.stateManager);

        // 检测上下文是否超长
        
        await this.context.messageService.say('tool', successMessage, false);
        await this.checkContextLength(userMessageContent, rootImageUrl);

        this.context.loggerManager.perf({
          namespace: ASSISTANT_NAMESPACE,
          subtag: 'kwaipilot-ide-agent-chat-tool',
          millis: Date.now() - startToolTime,
          extra4: 'success',
          extra6: block.name
        });

        ToolHelpers.reportToolAction(this.context, block, Date.now() - startToolTime, {
          url
        });
      }
    } catch (error: any) {
      await ToolHelpers.handleError(
        block,
        userMessageContent,
        this.context,
        generationCall,
        'parsing figma node',
        error,
        'parse_figma'
      );
    }
  }

  /**
   * 检测上下文长度是否超限
   * @param userMessageContent 当前用户消息内容
   */
  private async checkContextLength(userMessageContent: TextBlockParamVersion1[], rootImageUrl: string): Promise<void> {
    try {
      // 获取agentManager和contextManager
      const agentManager = this.context.agentManager;
      if (!agentManager?.contextManager) {
        return;
      }

      const contextManager = agentManager.contextManager;
      const stateManager = this.context.stateManager;
      const state = stateManager.getState();

      // 创建包含当前消息内容的临时消息
      const currentMessage = {
        role: 'user' as const,
        content: userMessageContent,
        chatId: state.chatId,
        version: 1
      };

      // 将当前消息添加到会话历史中进行检测
      const testMessages = [...state.apiConversationHistory, currentMessage];

      // 使用ContextManager进行优化处理，模拟真实的上下文处理流程
      const optimizedResult = await contextManager.optimizeMessagesContext(testMessages);
      const optimizedMessages = optimizedResult.messages;

      // 创建TokenCalculator实例来计算优化后消息的token数量
      const tokenCalculator = new TokenCalculator(state.modelConfig.model);
      const totalTokens = await tokenCalculator.calculate(JSON.stringify(optimizedMessages));
      const maxTokens = contextManager['state'].maxTokens;


      // 如果超长，输出"超长"
      if (totalTokens > maxTokens) {
        await this.context.messageService.say('tool', JSON.stringify({
          tool: 'figmaExtraLong',
          content: rootImageUrl
        } satisfies SayTool), false);
      }
    } catch (error) {
    }
  }


}
