import { TokenCalculator as ITokenCalculator } from '../types/context';
import { Logger } from '@/util/log';
import { GlobalConfig } from '@/util/global';
import { Api } from '@/http';

export class TokenCalculator implements ITokenCalculator {
  private logger = new Logger('token-calculator');
  private model: string;
  private httpClient = new Api();

  constructor(model: string) {
    // 在初始化时确定使用的模型
    this.model = model;
  }

  /**
   * 计算文本的token数量
   * @param text 需要计算token的文本
   * @returns 返回token数量
   */
  async calculate(text: string): Promise<number> {
    try {
      // 调用token计算API
      const result = await this.callTokenizerApi(text);
      return result;
    } catch (error) {
      this.logger.error('Failed to calculate tokens:', error);
      // 如果API调用失败，使用简单的字符数估算作为备用方案
      this.logger.info('Falling back to character-based token estimation');
      return this.estimateTokens(text);
    }
  }

  /**
   * 调用tokenizer API计算token数量
   * @param text 需要计算token的文本
   * @returns 返回token数量
   */
  private async callTokenizerApi(text: string): Promise<number> {
    try {
      const payload = {
        model: this.model,
        text: text
      };
      const s = Date.now();
      const { data } = await this.httpClient.post<number[]>(
        '/eapi/kwaipilot/plugin/tokenizer',
        JSON.stringify(payload)
      );
      this.logger.debug(`Token API response time: ${Date.now() - s} ms`);

      return data.length;
    } catch (error) {
      this.logger.error('Error calling tokenizer API:', error);
      throw error;
    }
  }

  /**
   * 使用简单的字符数估算token数量（作为备用方案）
   * @param text 需要估算token的文本
   * @returns 估算的token数量
   */
  private estimateTokens(text: string): number {
    // 简单的字符数估算，大约4个字符对应1个token
    return Math.ceil(text.length / 4);
  }
}
